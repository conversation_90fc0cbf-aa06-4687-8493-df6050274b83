#ifndef __SCREEN_H
#define __SCREEN_H

#include "main.h"

#define FAN_SCREEN_ID 0

#define FAN_STA_ID 1
#define FAN_DIR_ID 2
#define FAN_SPEED_ID 3
#define FAN_RUN_TIME_ID 7
#define FAN_COUNT_TIME_ID 6
#define FAN_VOL_ID 5
#define FAN_DIS_ID 4
#define FAN_LOCK_DIS_ID 8

void welcome(void);
void start_voice(void);
void stop_voice(void);

void update_screen_fan_status(uint8_t status);
void update_screen_fan_dir(uint8_t dir);
void update_screen_fan_speed(uint8_t speed);
void update_screen_time(uint8_t time);
void update_screen_count_time(uint8_t time);
void update_screen_voltage(float vol);
void update_screen_dis(float dis);
void update_screen_dis_lock(void);
void update_group_mode(uint8_t status);
uint8_t build_packet(uint8_t screen_buffer[], uint8_t data[], uint8_t data_len, uint8_t screen_id, uint8_t widget_id);
void send_to_screen_usart(uint16_t val, uint8_t screen_id, uint8_t widget_id);
#endif



























