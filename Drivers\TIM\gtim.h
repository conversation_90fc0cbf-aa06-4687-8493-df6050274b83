/**
 ****************************************************************************************************
 * @file        gtim.h
 * <AUTHOR>
 * @version     V1.0
 * @date        2023-08-01
 * @brief       ??????? ????????
 * @license     Copyright (c) 2020-2032, ??????????????????????
 ****************************************************************************************************
 * @attention
 *
 * ?????:??????? M48Z-M3??С????STM32F103??
 * ???????:www.yuanzige.com
 * ???????:www.openedv.com
 * ??????:www.alientek.com
 * ??????:openedv.taobao.com
 *
 ****************************************************************************************************
 */

#ifndef __GTIM_H
#define __GTIM_H

#include "main.h"

extern volatile uint8_t count_down;
extern volatile uint32_t tim4_cb_times;
extern volatile uint8_t tim4_rev_flag;
extern volatile uint8_t tim4_rev_flag_2s;
extern volatile uint32_t tim5_rev_flag_1s;

extern TIM_HandleTypeDef g_timxchy_cap_handle;  /* 定时器x句柄 */

/* ??????????? */
#define GTIM_TIMX_CAP_CHY_GPIO_PORT            GPIOB
#define GTIM_TIMX_CAP_CHY_GPIO_PIN             GPIO_PIN_7
#define GTIM_TIMX_CAP_CHY_GPIO_CLK_ENABLE()    do{ __HAL_RCC_GPIOB_CLK_ENABLE(); }while(0)

#define GTIM_TIMX_CAP                          TIM4
#define GTIM_TIMX_CAP_IRQn                     TIM4_IRQn
#define GTIM_TIMX_CAP_CHY                      TIM_CHANNEL_2                                 /* ???Y,  1<= Y <=4 */
#define GTIM_TIMX_CAP_CHY_CCRX                 TIM4->CCR2                                    /* ???Y???????????? */
#define GTIM_TIMX_CAP_CHY_CLK_ENABLE()         do{ __HAL_RCC_TIM4_CLK_ENABLE(); }while(0)    /* TIM4 ?????? */

/* ???????? */
void gtim_timx_cap_chy_init(uint16_t arr, uint16_t psc);                                    /* ??????? ?????????????? */


#endif

