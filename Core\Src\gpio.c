/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.c
  * @brief   This file provides code for the configuration
  *          of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"

/* USER CODE BEGIN 0 */
#include "ds.h"
#include "gtim.h"
#include "stdio.h"
/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as
        * Analog
        * Input
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOE_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_2, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOE, GPIO_PIN_15, GPIO_PIN_RESET);

  /*Configure GPIO pins : PE6 PE7 PE8 PE9 */
  GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

  /*Configure GPIO pin : PA2 */
  GPIO_InitStruct.Pin = GPIO_PIN_2;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : PE15 */
  GPIO_InitStruct.Pin = GPIO_PIN_15;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

  /* EXTI interrupt init*/
  HAL_NVIC_SetPriority(EXTI9_5_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI9_5_IRQn);

}

/* USER CODE BEGIN 2 */
volatile uint8_t S1 = NO_RESPONSE;   //光电S1~S4检测状态
volatile uint8_t S2 = NO_RESPONSE;
volatile uint8_t S3 = NO_RESPONSE;
volatile uint8_t S4 = NO_RESPONSE;
volatile uint8_t EXTI_flag = 0;
volatile enum G_Sn last_S = NONE;
volatile uint16_t S_queue = 0;

void clr_S(void)
{
	S_queue = 0;
	last_S = NONE;
	S1 = NO_RESPONSE;
	S2 = NO_RESPONSE;
	S3 = NO_RESPONSE;
	S4 = NO_RESPONSE;
}

//光电传感器中断
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
	printf("EXTI_Callback!!!  ");
	tim4_cb_times =0;   //定时器4回调次数清零
	
	if(GPIO_Pin == GPIO_PIN_6) //S1
	{
		if(last_S != gS1) {
			printf("--->S1 \r\n");
			S1 = RESPONSE;
			
			S_queue = S_queue*10+gS1;
			last_S = gS1;
		}
	}else if(GPIO_Pin == GPIO_PIN_7) //S2
	{
		if(last_S != gS2) {
			printf("--->S2 \r\n");
			S2 = RESPONSE;
			
			S_queue = S_queue*10+gS2;
			last_S = gS2;
		}
		
	}else if(GPIO_Pin == GPIO_PIN_8) //S3
	{
		if(last_S != gS3) {
			printf("--->S3 \r\n");
			S3 = RESPONSE;
			
			S_queue = S_queue*10+gS3;
			last_S = gS3;
		}
		
	}else if(GPIO_Pin == GPIO_PIN_9) //S4
	{
		if(last_S != gS4) {
			printf("--->S4 \r\n");
			S4 = RESPONSE;
			
			S_queue = S_queue*10+gS4;
			last_S = gS4;
		}
	}
	
	EXTI_flag = 1;  //中断标志位
}
/* USER CODE END 2 */
