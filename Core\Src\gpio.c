/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    gpio.c
  * @brief   This file provides code for the configuration
  *          of all used GPIO pins.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "gpio.h"

/* USER CODE BEGIN 0 */
#include "ds.h"
#include "gtim.h"
#include "stdio.h"
/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure GPIO                                                             */
/*----------------------------------------------------------------------------*/
/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/** Configure pins as
        * Analog
        * Input
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOE_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5|GPIO_PIN_6, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, GPIO_PIN_RESET);

  /*Configure GPIO pins : PC0 PC1 PC2 PC3 (光电开关，低电平触发) */
  GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
  GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*Configure GPIO pin : PA5 (风扇使能控制) */
  GPIO_InitStruct.Pin = GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : PA6 (风扇方向控制) */
  GPIO_InitStruct.Pin = GPIO_PIN_6;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pin : PB6 (超声波TRIG) */
  GPIO_InitStruct.Pin = GPIO_PIN_6;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /* EXTI interrupt init*/
  HAL_NVIC_SetPriority(EXTI0_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI0_IRQn);
  HAL_NVIC_SetPriority(EXTI1_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI1_IRQn);
  HAL_NVIC_SetPriority(EXTI2_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI2_IRQn);
  HAL_NVIC_SetPriority(EXTI3_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI3_IRQn);

}

/* USER CODE BEGIN 2 */
volatile uint8_t S1 = NO_RESPONSE;   //光电S1~S4检测状态
volatile uint8_t S2 = NO_RESPONSE;
volatile uint8_t S3 = NO_RESPONSE;
volatile uint8_t S4 = NO_RESPONSE;
volatile uint8_t EXTI_flag = 0;
volatile enum G_Sn last_S = NONE;
volatile uint16_t S_queue = 0;

void clr_S(void)
{
	S_queue = 0;
	last_S = NONE;
	S1 = NO_RESPONSE;
	S2 = NO_RESPONSE;
	S3 = NO_RESPONSE;
	S4 = NO_RESPONSE;
}

//光电传感器中断
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
	printf("EXTI_Callback!!!  ");
	tim4_cb_times =0;   //定时器4回调次数清零

	if(GPIO_Pin == GPIO_PIN_0) //S1 (PC0)
	{
		if(last_S != gS1) {
			printf("--->S1 \r\n");
			S1 = RESPONSE;

			S_queue = S_queue*10+gS1;
			last_S = gS1;
		}
	}else if(GPIO_Pin == GPIO_PIN_1) //S2 (PC1)
	{
		if(last_S != gS2) {
			printf("--->S2 \r\n");
			S2 = RESPONSE;

			S_queue = S_queue*10+gS2;
			last_S = gS2;
		}

	}else if(GPIO_Pin == GPIO_PIN_2) //S3 (PC2)
	{
		if(last_S != gS3) {
			printf("--->S3 \r\n");
			S3 = RESPONSE;

			S_queue = S_queue*10+gS3;
			last_S = gS3;
		}

	}else if(GPIO_Pin == GPIO_PIN_3) //S4 (PC3)
	{
		if(last_S != gS4) {
			printf("--->S4 \r\n");
			S4 = RESPONSE;

			S_queue = S_queue*10+gS4;
			last_S = gS4;
		}
	}

	EXTI_flag = 1;  //中断标志位
}
/* USER CODE END 2 */
