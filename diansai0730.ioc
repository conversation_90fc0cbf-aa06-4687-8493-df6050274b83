#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_0
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,master,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,ExternalTrigConv
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_1CYCLE_5
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_NONE
DAC.IPParameters=DAC_Trigger
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103ZET6
Mcu.Family=STM32F1
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=USART1
Mcu.IP11=USART3
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=TIM2
Mcu.IP6=TIM3
Mcu.IP7=TIM4
Mcu.IP8=TIM5
Mcu.IP9=TIM6
Mcu.IPNb=12
Mcu.Name=STM32F103Z(C-D-E)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE6
Mcu.Pin1=PC14-OSC32_IN
Mcu.Pin10=PE8
Mcu.Pin11=PE9
Mcu.Pin12=PE15
Mcu.Pin13=PB11
Mcu.Pin14=PD8
Mcu.Pin15=PD9
Mcu.Pin16=PA9
Mcu.Pin17=PA10
Mcu.Pin18=PA13
Mcu.Pin19=PA14
Mcu.Pin2=PC15-OSC32_OUT
Mcu.Pin20=VP_SYS_VS_Systick
Mcu.Pin21=VP_TIM2_VS_ClockSourceINT
Mcu.Pin22=VP_TIM3_VS_ClockSourceINT
Mcu.Pin23=VP_TIM4_VS_ClockSourceINT
Mcu.Pin24=VP_TIM5_VS_ClockSourceINT
Mcu.Pin25=VP_TIM6_VS_ClockSourceINT
Mcu.Pin3=OSC_IN
Mcu.Pin4=OSC_OUT
Mcu.Pin5=PA0-WKUP
Mcu.Pin6=PA2
Mcu.Pin7=PA4
Mcu.Pin8=PA6
Mcu.Pin9=PE7
Mcu.PinsNb=26
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103ZETx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC1_2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI9_5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
OSC_IN.Mode=HSE-External-Oscillator
OSC_IN.Signal=RCC_OSC_IN
OSC_OUT.Mode=HSE-External-Oscillator
OSC_OUT.Signal=RCC_OSC_OUT
PA0-WKUP.Signal=ADCx_IN0
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Signal=GPIO_Output
PA4.Signal=COMP_DAC1_group
PA6.Signal=S_TIM3_CH1
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB11.Signal=S_TIM2_CH4
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE15.Locked=true
PE15.Signal=GPIO_Output
PE6.Locked=true
PE6.Signal=GPXTI6
PE7.Locked=true
PE7.Signal=GPXTI7
PE8.Locked=true
PE8.Signal=GPXTI8
PE9.Locked=true
PE9.Signal=GPXTI9
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=diansai0730.ioc
ProjectManager.ProjectName=diansai0730
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DAC_Init-DAC-false-HAL-true,4-MX_TIM2_Init-TIM2-false-HAL-true,5-MX_USART1_UART_Init-USART1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_TIM4_Init-TIM4-false-HAL-true,8-MX_TIM5_Init-TIM5-false-HAL-true,9-MX_TIM6_Init-TIM6-false-HAL-true,10-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.ADCFreqValue=12000000
RCC.ADCPresc=RCC_ADCPCLK2_DIV6
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FSMCFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.I2S2Freq_Value=72000000
RCC.I2S3Freq_Value=72000000
RCC.IPParameters=ADCFreqValue,ADCPresc,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FSMCFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SDIOFreq_Value=72000000
RCC.SDIOHCLKDiv2FreqValue=36000000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.ADCx_IN0.0=ADC1_IN0,IN0
SH.ADCx_IN0.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SH.GPXTI6.0=GPIO_EXTI6
SH.GPXTI6.ConfNb=1
SH.GPXTI7.0=GPIO_EXTI7
SH.GPXTI7.ConfNb=1
SH.GPXTI8.0=GPIO_EXTI8
SH.GPXTI8.ConfNb=1
SH.GPXTI9.0=GPIO_EXTI9
SH.GPXTI9.ConfNb=1
SH.S_TIM2_CH4.0=TIM2_CH4,Input_Capture4_from_TI4
SH.S_TIM2_CH4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
TIM2.Channel-Input_Capture4_from_TI4=TIM_CHANNEL_4
TIM2.IPParameters=Prescaler,Period,TIM_MasterOutputTrigger,Channel-Input_Capture4_from_TI4
TIM2.Period=65535
TIM2.Prescaler=71
TIM2.TIM_MasterOutputTrigger=TIM_TRGO_RESET
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.IPParameters=Channel-PWM Generation1 CH1,Prescaler,Period,TIM_MasterOutputTrigger
TIM3.Period=999
TIM3.Prescaler=7199
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM4.IPParameters=Prescaler,Period
TIM4.Period=999
TIM4.Prescaler=7199
TIM5.IPParameters=Prescaler,Period
TIM5.Period=9999
TIM5.Prescaler=7199
TIM6.IPParameters=Prescaler
TIM6.Prescaler=71
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.BaudRate=9600
USART3.IPParameters=VirtualMode,BaudRate
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
VP_TIM5_VS_ClockSourceINT.Mode=Internal
VP_TIM5_VS_ClockSourceINT.Signal=TIM5_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
