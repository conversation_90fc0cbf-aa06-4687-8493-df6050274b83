# 硬件引脚配置修改总结

## 修改前后对比

### 原配置：
- **串口屏**：使用UART3，PD8(TX)、PD9(RX)，波特率9600
- **光电开关**：PE6-S1、PE7-S2、PE8-S3、PE9-S4（上升沿触发）
- **风扇控制**：
  - 速度控制：PA4 (DAC_OUT1)
  - 方向控制：PE15（高电平正转，低电平反转）
  - 使能控制：缺少
- **ADC反馈**：PA0-WKUP (ADC1_IN0)
- **超声波测距**：PA2(TRIG)、PA3(ECHO，通过TIM2_CH4输入捕获)

### 新配置：
- **串口**：使用UART1，PA9(TX)、PA10(RX)，波特率115200
- **光电开关S1-S4**：PC0-PC3（低电平触发）
- **超声波测距S5**：PB6(TRIG)、PB7(ECHO，通过TIM4_CH2输入捕获)
- **风扇控制**：
  - 速度控制：PA4 (DAC1_CH1，模拟电压输出0-3.3V)
  - 方向控制：PA6（数字IO）
  - 使能控制：PA5（数字IO）

## 修改的文件列表

### 1. Core/Src/main.c
- 更新硬件连接注释
- 更新光电开关引脚注释（PE6-PE9 → PC0-PC3）
- 禁用USART3和ADC1初始化
- 修改定时器初始化（TIM2 → TIM4，CH4 → CH2）
- 添加风扇控制初始化调用

### 2. Core/Src/gpio.c
- 修改GPIO输出电平设置（PA2、PE15 → PA5、PA6、PB6）
- 修改光电开关GPIO配置（PE6-PE9 → PC0-PC3）
- 修改中断触发方式（上升沿 → 下降沿）
- 添加上拉电阻配置
- 添加风扇控制引脚配置（PA5使能、PA6方向）
- 添加超声波TRIG引脚配置（PB6）
- 修改EXTI中断配置（EXTI9_5 → EXTI0-EXTI3）
- 修改光电开关中断回调函数引脚判断

### 3. Core/Src/stm32f1xx_it.c
- 删除EXTI9_5_IRQHandler
- 添加EXTI0_IRQHandler、EXTI1_IRQHandler、EXTI2_IRQHandler、EXTI3_IRQHandler

### 4. Drivers/ATK-MB019/atk_ultrasonic.h
- 修改TRIG引脚定义（GPIOA/GPIO_PIN_2 → GPIOB/GPIO_PIN_6）

### 5. Drivers/TIM/gtim.h
- 修改超声波ECHO引脚定义（GPIOA/GPIO_PIN_3 → GPIOB/GPIO_PIN_7）
- 修改定时器配置（TIM2 → TIM4，CH4 → CH2）
- 添加定时器句柄声明

### 6. Drivers/TIM/gtim.c
- 添加定时器句柄定义
- 完善gtim_timx_cap_chy_init函数实现
- 修改中断回调函数中的定时器句柄引用

### 7. Drivers/DS/ds.h
- 添加ctr_fan_init函数声明

### 8. Drivers/DS/ds.c
- 添加ctr_fan_init函数实现
- 修改ctr_fan_speed_dir函数（PE15 → PA6方向控制，添加PA5使能控制）
- 修改ctr_set_voltage函数（添加风扇停止时的使能控制）

## 功能变化

### 1. 串口通信
- 从UART3切换到UART1
- 波特率从9600提升到115200

### 2. 光电开关
- 引脚从PE6-PE9改为PC0-PC3
- 触发方式从上升沿改为下降沿（低电平触发）
- 添加上拉电阻

### 3. 超声波测距
- TRIG引脚从PA2改为PB6
- ECHO引脚从PA3改为PB7
- 定时器从TIM2_CH4改为TIM4_CH2

### 4. 风扇控制
- 方向控制从PE15改为PA6
- 新增使能控制PA5
- 速度控制保持PA4不变

### 5. 移除功能
- 移除ADC反馈功能（PA0）
- 移除USART3配置

## 注意事项

1. **定时器冲突**：需要确保TIM4既用于输入捕获（超声波）又用于基本定时（系统定时），可能需要进一步调整
2. **中断优先级**：新增的EXTI0-EXTI3中断需要合理设置优先级
3. **风扇使能逻辑**：添加了使能控制，需要确保启动和停止逻辑正确
4. **测试验证**：所有修改需要在实际硬件上进行测试验证

## 编译和测试建议

1. 编译项目检查是否有语法错误
2. 检查所有引脚定义是否与实际硬件连接一致
3. 测试光电开关的低电平触发是否正常工作
4. 测试超声波测距功能
5. 测试风扇的启动、停止、方向控制
6. 验证串口通信功能
