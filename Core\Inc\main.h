/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f1xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/

/* USER CODE BEGIN Private defines */

/*
enum FAN_STA
{
	FAN_START=1,   //����
  FAN_STOP,   //ֹͣ
  FAN_FORWARD_DIR,   //��ת
  FAN_REVERSE_DIR,   //��ת
  FAN_SET_TIME,    //��������ʱ��
  FAN_SET_VOL,    //���õ�ѹ��ת�٣�
  FAN_VOL_DOWN,   //��ѹ-
  FAN_VOL_UP   //��ѹ+
};*/

#define	FAN_START 1   //����
#define FAN_STOP 2    //ֹͣ
#define FAN_FORWARD_DIR 3   //��ת
#define FAN_REVERSE_DIR 4   //��ת
#define FAN_SET_TIME 5    //��������ʱ��
#define FAN_SET_VOL 6     //���õ�ѹ��ת�٣�
#define FAN_VOL_DOWN 7   //��ѹ-
#define FAN_VOL_UP 8   //��ѹ+

enum SYS_STA
{
	SYS_NORMAL=1,   //��������
  SYS_SET_GROUP,   //�������ģʽ
  SYS_RUN_GROUP   //�������ģʽ
};

extern uint8_t save_distance;
extern enum SYS_STA cur_sys_state;
extern uint8_t group_done_flag;
extern uint8_t start_down_count;
/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
