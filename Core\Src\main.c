/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dac.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stdio.h"
#include "atk_ultrasonic.h"
#include "delay.h"
#include "stdlib.h"
#include "screen.h"
#include "ds.h"
#include "gtim.h"
#include "string.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

//手掌停留测距的距离值
uint8_t save_distance = 0;
uint8_t group_queue[20]={0};
volatile uint8_t queue_index = 0;

enum SYS_STA cur_sys_state= SYS_NORMAL;  //当前系统运行状态
uint8_t group_done_flag = 0;  //组合模式当前节点是否完成标志位  0-空  1-有任务在执行

uint8_t start_down_count = 0;  //开始倒计时标志位

/* USER CODE END 0 */

/***************************************************************************************
硬件连接
 -串口 使用uart1
	PA9     ------> USART1_TX
	PA10    ------> USART1_RX
	波特率115200
 -光电开关S1-S4 (低电平触发)
	PC0-S1  	PC1-S2
	PC2-S3      PC3-S4
 -超声波测距S5
	PB6     ------> TRIG
	PB7     ------> ECHO
 -风扇控制
	速度控制	PA4     ------> DAC1_CH1 (模拟电压输出0-3.3V)
	方向控制	PA6     ------> 数字IO (高电平正转，低电平反转)
	使能控制	PA5     ------> 数字IO (高电平使能，低电平禁用)

****************************************************************************************/



/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DAC_Init();
  MX_TIM2_Init();
  MX_USART1_UART_Init();
  MX_TIM3_Init();
  MX_TIM4_Init();
  MX_TIM5_Init();
  MX_TIM6_Init();
  // MX_USART3_UART_Init(); // 不再使用USART3
  // MX_ADC1_Init(); // 不再使用ADC反馈
  /* USER CODE BEGIN 2 */

	delay_init(72);     /* 延时函数初始化 */
	ctr_fan_init();     /* 风扇控制初始化 */
	HAL_TIM_Base_Start_IT(&htim4);
	HAL_TIM_IC_Start_IT(&htim4, TIM_CHANNEL_2);
	HAL_TIM_Base_Start(&htim3);
	HAL_TIM_PWM_Start(&htim3, TIM_CHANNEL_1);
	HAL_TIM_Base_Start_IT(&htim4);
	HAL_TIM_Base_Start_IT(&htim5);
	HAL_DAC_Start(&hdac, DAC_CHANNEL_1);
	
	// HAL_ADCEx_Calibration_Start(&hadc1); // 不再使用ADC
	// HAL_Delay(10);
	// HAL_ADC_Start_IT(&hadc1);  // 不再使用ADC
	
	printf("start!!!! \r\n");

	update_group_mode(STOP);
	update_screen_fan_status(STOP);
	
	welcome();
	
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
		//如果当前非运行组合模式，则正常处理光电感应
		if(cur_sys_state != SYS_RUN_GROUP)
		{
			//PC0-S1 PC1-S2  PC2-S3   PC3-S4
			if(tim4_rev_flag_2s)
			{
				tim4_rev_flag_2s = 0;
				//printf("S_queue=%d\r\n",S_queue);
				//S2->S1的所有可能路径
				if(S_queue==21||S_queue==321||S_queue==231||S_queue==421
					||S_queue==214||S_queue==241||S_queue==3214||S_queue==3241||S_queue==2314||S_queue==2341)
				{
					printf("S2--->S1 (fan stop)\r\n");
					//**********S2->S1 风扇停止**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_STOP;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_set_voltage(0);  //输出电压为0-停止
					}
				}
				//S3->S1的所有可能路径
				else if(S_queue==31||S_queue==321||S_queue==3214||S_queue==314||S_queue==341)
				{
					printf("S3--->S1 (vol down)\r\n");
					//**********S3->S1 电压下降**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_VOL_DOWN;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_voltage_down();
					}
				}
				//S4->S1的所有可能路径
				else if(S_queue==41||S_queue==414)
				{
					printf("S4--->S1 (set vol)\r\n");
					//**********S4->S1 根据距离设置电压值**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						float vol = ctr_dis_get_voltage();
						if(vol != 0){
							group_queue[queue_index]=FAN_SET_VOL;
							queue_index++;
						
							group_queue[queue_index]=(int)(vol*10);
							queue_index++;
						}
						update_screen_voltage(vol);
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_dis_set_voltage();
					}
				}
				//S1->S2的所有可能路径
				else if(S_queue==4123||S_queue==1423||S_queue==1432||S_queue==4132||S_queue==12
					||S_queue==412||S_queue==123||S_queue==142||S_queue==132)
				{
					printf("S1--->S2 (fan forward)\r\n");
					//**********S1->S2 风扇正转**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_FORWARD_DIR;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_fan_speed_dir(FORWARD);
						start_down_count = 1;
					}
				}
				//S3->S2的所有可能路径
				else if(S_queue==32||S_queue==323)
				{
					//**********S3->S2 根据距离设置定时器值**********//
					printf("S3--->S2 (set time)\r\n");
					ctr_dis_set_time();
					
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_SET_TIME;
						queue_index++;
						group_queue[queue_index]=count_down;
						queue_index++;
					}
				}
				//S4->S2的所有可能路径
				else if(S_queue==42||S_queue==423||S_queue==432)
				{
					printf("S4--->S2 (vol up) \r\n");
					//**********S4->S2 电压上升**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_VOL_UP;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_voltage_up();
					}
				}
				
				//S1->S3的所有可能路径
				else if(S_queue==13 || S_queue==143 || S_queue==413)
				{
					//**********S1->S3 关闭语音*********//
					printf("S1--->S3 (stop voice)\r\n");
					stop_voice();
				}
				
				//S2->S3的所有可能路径
				else if(S_queue==23)
				{
					//**********S2->S3 退出组合模式**********//
					printf("S2--->S3 (exit group)\r\n");
					//执行序列
					for(uint8_t i=0;i<queue_index;i++)
					{
						printf("-->%d ", group_queue[i]);
					}
					printf("\r\n");
					
					count_down = 0;  //重置倒计时时间
					queue_index = 0; //重置索引
					cur_sys_state = SYS_RUN_GROUP; //运行组合模式
				}
				//S4->S3的所有可能路径
				else if(S_queue==43)
				{
					printf("S4--->S3 (fan stop)\r\n");
					//**********S4->S3 停止**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_STOP;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_set_voltage(0);  //输出电压为0-停止
					}
				}
				//S1->S4的所有可能路径
				else if(S_queue==14)
				{
					//**********S1->S4 启动组合模式**********//
					printf("S1--->S4 (enter group)\r\n");
					memset(group_queue,0,sizeof(group_queue));
					queue_index=0;
					cur_sys_state = SYS_SET_GROUP;
					update_group_mode(RUN);
				}
				//S2->S4的所有可能路径
				else if(S_queue==24 || S_queue==324 || S_queue==234)
				{
					//**********S2->S4 开启语音*********//
					printf("S2--->S4 (start voice)\r\n");
					start_voice();
				}
				
				//S3->S4的所有可能路径
				else if(S_queue==34)
				{
					printf("S3--->S4 (fan reverse)\r\n");
					//**********S3->S4 风扇反向启动**********//
					if(cur_sys_state == SYS_SET_GROUP) //组合模式
					{
						group_queue[queue_index]=FAN_REVERSE_DIR;
						queue_index++;
					}
					else if(cur_sys_state == SYS_NORMAL)  //非组合模式
					{
						ctr_fan_speed_dir(REVERSE);
						start_down_count = 1;
					}
				}
				//最后清空序列
				clr_S();
			}
		}
		//运行组合模式
		else
		{
			//当前无任务，继续下一任务
			if(group_done_flag == 0)
			{
				switch(group_queue[queue_index])
				{
					case FAN_START:
						printf("switch FAN_START \r\n");
						break;
					case FAN_STOP:
						printf("switch FAN_STOP \r\n");
						break;
					case FAN_FORWARD_DIR:
						printf("switch FAN_FORWARD_DIR \r\n");
						ctr_fan_speed_dir(FORWARD);
					  queue_index++;
						break;
					case FAN_REVERSE_DIR:
						printf("switch FAN_REVERSE_DIR \r\n");
						ctr_fan_speed_dir(REVERSE);
						queue_index++;
						break;
					case FAN_SET_TIME:
						//只有当上一次倒计时时间到了才能设置下一次---为了规避组合情况，后续想办法优化
						if(count_down <= 0) {
							printf("switch FAN_SET_TIME \r\n");
							//group_done_flag = 1;
							queue_index++;
							uint8_t time = group_queue[queue_index];
							printf("switch time:%d \r\n", time);
							ctr_dis_set_time_1(time);
							update_screen_time(time);
							start_down_count = 1;
							queue_index++;
						}
						break;
					case FAN_SET_VOL:
						printf("switch FAN_SET_VOL \r\n");
						queue_index++;
					  float vol = group_queue[queue_index]/10.0;
					  ctr_set_voltage(vol);
						queue_index++;
						break;
					case FAN_VOL_DOWN:
						printf("switch FAN_VOL_DOWN \r\n");
						break;
					case FAN_VOL_UP:
						printf("switch FAN_VOL_UP \r\n");
						break;
					default:  //所有指令执行完成，恢复为正常模式
						printf("switch end!!! \r\n");
						cur_sys_state = SYS_NORMAL;  
						update_group_mode(STOP);
						//清空序列
						memset(group_queue,0,sizeof(group_queue));
						queue_index=0;
						break;
				}
			}
		}
		
		if(tim4_rev_flag)
		{
			tim4_rev_flag = 0;
			//每100ms测一次距离
			update_distance();
		}
		
		if(tim5_rev_flag_1s)
		{
			//printf("adc_value: %d voltage=%.2f \r\n", adc_value, voltage);
			
			tim5_rev_flag_1s = 0;
			if(count_down == 0)
			{
				group_done_flag = 0;   //倒计时为0时，置位关键标志位
				start_down_count = 0;
			}
			if((start_down_count == 1 || cur_sys_state == SYS_RUN_GROUP) && count_down > 0) //非设置组合模式
			{
				count_down--;
				update_screen_count_time(count_down);
				
				//倒计时到0时，需要停止风扇
				if(count_down == 0)
				{
					ctr_set_voltage(0);
					start_down_count = 0;
					update_screen_time(0);
					update_screen_dis(0);
				}
			}
		}

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_ADC;
  PeriphClkInit.AdcClockSelection = RCC_ADCPCLK2_DIV6;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
