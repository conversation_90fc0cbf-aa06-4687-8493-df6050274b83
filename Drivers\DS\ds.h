#ifndef __DS_H
#define __DS_H

#include "main.h"

#define FORWARD 1
#define REVERSE 0

#define RUN 1
#define STOP 0

extern volatile float current_vol;
extern volatile uint16_t  cur_set_da_value;

void ctr_fan_init(void);
void ctr_fan_speed_dir(uint8_t dir);
void ctr_set_voltage(float vol);
void ctr_voltage_up(void);
void ctr_voltage_down(void);
void ctr_dis_set_voltage(void);
float ctr_dis_get_voltage(void);
void ctr_dis_set_time(void);
void ctr_dis_set_time_1(uint8_t time);
uint16_t query_da_value(float vol);

#endif



























