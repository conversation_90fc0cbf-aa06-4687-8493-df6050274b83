#include "ds.h"
#include "tim.h"
#include "dac.h"
#include "gtim.h"
#include "screen.h"
#include "stdio.h"
#include "math.h"

#define DEFAULT_VOL 6  //直接启动的话，默认给6V电压
volatile float current_vol = 0;
volatile uint16_t  cur_set_da_value = 0;   //当前设置的DA值 0~ 4095

const float vol_table[]={10.5, 10.0, 9.5, 9.0, 8.5, 8.0, 7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0};
const float vol_da_table[]={2690, 2570, 2435, 2300, 2190, 2050, 1930, 1800, 1680, 1540, 1410, 1280, 1150, 1030, 890, 770};
const float vol_speed_table[]={5950, 5612, 5350, 5143, 4814, 4576, 4212, 3940, 3600, 3320, 3012, 2651, 2382, 2017, 1742, 1344};

//动作一二：风扇正反转
void ctr_fan_speed_dir(uint8_t dir)
{
	if(dir == FORWARD)  //dir=1 风扇正转
	{
		HAL_GPIO_WritePin(GPIOE, GPIO_PIN_15, GPIO_PIN_SET);
	}
	else   //dir=0 风扇反转
	{
		HAL_GPIO_WritePin(GPIOE, GPIO_PIN_15, GPIO_PIN_RESET);
	}
	//如果此时风速是0，要给一个默认值
	if(current_vol == 0)
	{
		ctr_set_voltage(DEFAULT_VOL);
	}
	
	update_screen_fan_dir(dir);  //更新屏幕转向
	HAL_Delay(100);
	update_screen_fan_status(RUN);  //更新屏幕启停状态
}

//动作三：电压上升 PWM波  DAC PA4
void ctr_voltage_up(void)
{
	//以0.5V的速度上升
	//只有正在运行时才能调速
	if(current_vol > 0 && current_vol <= 10){
		current_vol += 0.5;
		
		ctr_set_voltage(current_vol);
		
		update_screen_fan_status(RUN);  //速度>0为启动
		update_screen_voltage(current_vol); //更新屏幕电压值
		
		update_screen_fan_status(RUN);  //修改图标
	}
}

//动作四：电压下降 PWM波  DAC PA4
void ctr_voltage_down(void)
{
	//以0.5V的速度下降
	//只有正在运行时才能调速
	if(current_vol >= 0.5){
		current_vol -= 0.5;
		ctr_set_voltage(current_vol);
	}
}

//调整指定的电压值
void ctr_set_voltage(float vol)
{
	printf("set voltage=%.2f \r\n", vol);
	cur_set_da_value = query_da_value(vol);
	
	HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, cur_set_da_value); /* 12位右对齐数据格式设置DAC值 */
	update_screen_voltage(vol); //更新屏幕电压值
	if(vol>0)
	{
		update_screen_fan_status(RUN);  //修改图标
	}else{
		update_screen_fan_status(STOP);  //修改图标
	}
	current_vol = vol;
}

//根据表查值
uint16_t query_da_value(float vol)
{
	for(int i = 0; i < 16; i++)
	{
		if(vol == vol_table[i])
		{
			return vol_da_table[i];
		}
	}
	return 0;
}


//动作五：根据距离设时间  //定时器回调在gtim.c中
void ctr_dis_set_time(void)
{
	//只有在有效范围才设置
	if(save_distance < 21 && save_distance >= 5){
		uint8_t time = -1*((int)save_distance) + 35;
		ctr_dis_set_time_1(time);
		update_screen_time(time);
	}else{
		printf("set time dis err: %d!!! \r\n", save_distance);
	}
}

void ctr_dis_set_time_1(uint8_t time)
{
	count_down = time;
	update_screen_count_time(count_down);
	printf("set time=%d \r\n", count_down);
}

//动作六：根据距离设电压
void ctr_dis_set_voltage(void)
{
	printf("ctr_dis_set_voltage= %d \r\n", save_distance);
	//只有在有效范围才设置
	if(save_distance <= 20 && save_distance >= 5){
		float vol = -0.5*save_distance + 13;
		ctr_set_voltage(vol);
	}else{
		printf("set dis err: %d!!! \r\n", save_distance);
	}
	
}

//根据距离获取电压
float ctr_dis_get_voltage(void)
{
	//只有在有效范围才设置
	if(save_distance <= 20 && save_distance >= 5){
		float vol = -0.5*save_distance + 13;
		return vol;
	}else{
		printf("set dis err: %d!!! \r\n", save_distance);
	}
	return 0;
}


//动作七：进入组合操作模式
//动作八：退出组合操作模式







